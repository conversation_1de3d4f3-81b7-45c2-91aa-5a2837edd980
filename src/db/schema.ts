import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// 用户表
export const users = sqliteTable('users', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	// 使用dingTalkUnionId作为主要标识（可选，用于钉钉登录）
	dingTalkUnionId: text('dingTalkUnionId'),
	// 邮箱密码登录字段
	email: text('email'),
	password: text('password'),
	// 用户基本信息
	isAdmin: integer('isAdmin', { mode: 'boolean' }).notNull().default(false),
	token: integer('token').notNull().default(20000),
	requestTimes: integer('requestTimes').notNull().default(0),
	dingTalkUserId: text('dingTalkUserId'),
	name: text('name'),
	avatar: text('avatar'),
	mobile: text('mobile'),
	createdAt: text('createdAt').notNull().default(sql`(datetime('now'))`),
	updatedAt: text('updatedAt').notNull().default(sql`(datetime('now'))`),
});




// 导出类型
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

// 统计信息表
export const statistics = sqliteTable('statistics', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	totalRequestTimes: integer('totalRequestTimes').notNull().default(0),
	totalTokenUsage: integer('totalTokenUsage').notNull().default(0),
});

// 导出类型
export type Statistic = typeof statistics.$inferSelect;
export type NewStatistic = typeof statistics.$inferInsert;