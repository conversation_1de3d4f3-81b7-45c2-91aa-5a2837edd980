import { createFileRoute } from '@tanstack/react-router';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
	Settings, 
	Bell, 
	Moon, 
	Sun, 
	Globe, 
	Shield, 
	Database,
	Palette,
	Monitor,
	Volume2,
	Save
} from "lucide-react";
import * as React from "react";

export const Route = createFileRoute('/setting')({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<div className="min-h-screen bg-gray-50">
			<div className="max-w-4xl mx-auto p-6">
				{/* 页面标题 */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900">系统设置</h1>
					<p className="text-gray-600 mt-2">管理您的应用偏好设置和系统配置</p>
				</div>

				<div className="space-y-6">
					<AppearanceSettings />
					<NotificationSettings />
					<LanguageSettings />
					<PrivacySettings />
					<DataSettings />
				</div>
			</div>
		</div>
	);
}

// 外观设置组件
function AppearanceSettings() {
	const [theme, setTheme] = React.useState('system');
	const [fontSize, setFontSize] = React.useState('medium');

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center">
					<Palette className="h-5 w-5 mr-2" />
					外观设置
				</CardTitle>
				<CardDescription>
					自定义应用的外观和主题
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* 主题设置 */}
				<div className="space-y-3">
					<label className="text-sm font-medium text-gray-700">主题模式</label>
					<div className="flex space-x-3">
						{[
							{ value: 'light', label: '浅色', icon: Sun },
							{ value: 'dark', label: '深色', icon: Moon },
							{ value: 'system', label: '跟随系统', icon: Monitor }
						].map(({ value, label, icon: Icon }) => (
							<button
								key={value}
								onClick={() => setTheme(value)}
								className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
									theme === value 
										? 'border-blue-500 bg-blue-50 text-blue-700' 
										: 'border-gray-200 hover:bg-gray-50'
								}`}
							>
								<Icon className="h-4 w-4" />
								<span className="text-sm">{label}</span>
							</button>
						))}
					</div>
				</div>

				{/* 字体大小 */}
				<div className="space-y-3">
					<label className="text-sm font-medium text-gray-700">字体大小</label>
					<div className="flex space-x-3">
						{[
							{ value: 'small', label: '小' },
							{ value: 'medium', label: '中' },
							{ value: 'large', label: '大' }
						].map(({ value, label }) => (
							<button
								key={value}
								onClick={() => setFontSize(value)}
								className={`px-4 py-2 rounded-lg border transition-colors ${
									fontSize === value 
										? 'border-blue-500 bg-blue-50 text-blue-700' 
										: 'border-gray-200 hover:bg-gray-50'
								}`}
							>
								<span className="text-sm">{label}</span>
							</button>
						))}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

// 通知设置组件
function NotificationSettings() {
	const [notifications, setNotifications] = React.useState({
		dingtalk: true,
		push: false,
		sound: true,
		desktop: true
	});

	const toggleNotification = (type: keyof typeof notifications) => {
		setNotifications(prev => ({ ...prev, [type]: !prev[type] }));
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center">
					<Bell className="h-5 w-5 mr-2" />
					通知设置
				</CardTitle>
				<CardDescription>
					管理您接收通知的方式
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{[
					{ key: 'dingtalk', label: '钉钉通知', description: '接收重要更新的钉钉通知' },
					{ key: 'push', label: '推送通知', description: '接收浏览器推送通知' },
					{ key: 'sound', label: '声音提醒', description: '播放通知声音' },
					{ key: 'desktop', label: '桌面通知', description: '显示桌面通知' }
				].map(({ key, label, description }) => (
					<div key={key} className="flex items-center justify-between py-2">
						<div className="space-y-1">
							<p className="text-sm font-medium text-gray-900">{label}</p>
							<p className="text-xs text-gray-500">{description}</p>
						</div>
						<button
							onClick={() => toggleNotification(key as keyof typeof notifications)}
							className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
								notifications[key as keyof typeof notifications] 
									? 'bg-blue-600' 
									: 'bg-gray-200'
							}`}
						>
							<span
								className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
									notifications[key as keyof typeof notifications] 
										? 'translate-x-6' 
										: 'translate-x-1'
								}`}
							/>
						</button>
					</div>
				))}
			</CardContent>
		</Card>
	);
}

// 语言设置组件
function LanguageSettings() {
	const [language, setLanguage] = React.useState('zh-CN');
	const [timezone, setTimezone] = React.useState('Asia/Shanghai');

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center">
					<Globe className="h-5 w-5 mr-2" />
					语言和地区
				</CardTitle>
				<CardDescription>
					设置您的语言偏好和时区
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* 语言选择 */}
				<div className="space-y-3">
					<label className="text-sm font-medium text-gray-700">显示语言</label>
					<select 
						value={language} 
						onChange={(e) => setLanguage(e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
						<option value="zh-CN">简体中文</option>
						<option value="zh-TW">繁體中文</option>
						<option value="en-US">English (US)</option>
						<option value="ja-JP">日本語</option>
					</select>
				</div>

				{/* 时区选择 */}
				<div className="space-y-3">
					<label className="text-sm font-medium text-gray-700">时区</label>
					<select 
						value={timezone} 
						onChange={(e) => setTimezone(e.target.value)}
						className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
						<option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
						<option value="Asia/Tokyo">日本标准时间 (UTC+9)</option>
						<option value="America/New_York">美国东部时间 (UTC-5)</option>
						<option value="Europe/London">格林威治时间 (UTC+0)</option>
					</select>
				</div>
			</CardContent>
		</Card>
	);
}

// 隐私设置组件
function PrivacySettings() {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center">
					<Shield className="h-5 w-5 mr-2" />
					隐私与安全
				</CardTitle>
				<CardDescription>
					管理您的隐私设置和安全选项
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="space-y-4">
					<div className="flex items-center justify-between py-2">
						<div>
							<p className="text-sm font-medium text-gray-900">数据收集</p>
							<p className="text-xs text-gray-500">允许收集匿名使用数据以改进服务</p>
						</div>
						<Badge variant="outline">已启用</Badge>
					</div>
					
					<div className="flex items-center justify-between py-2">
						<div>
							<p className="text-sm font-medium text-gray-900">Cookie 设置</p>
							<p className="text-xs text-gray-500">管理网站 Cookie 和跟踪设置</p>
						</div>
						<Button variant="outline" size="sm">管理</Button>
					</div>

					<div className="flex items-center justify-between py-2">
						<div>
							<p className="text-sm font-medium text-gray-900">两步验证</p>
							<p className="text-xs text-gray-500">为您的账户添加额外的安全保护</p>
						</div>
						<Button variant="outline" size="sm">设置</Button>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

// 数据设置组件
function DataSettings() {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center">
					<Database className="h-5 w-5 mr-2" />
					数据管理
				</CardTitle>
				<CardDescription>
					管理您的数据存储和备份设置
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="space-y-4">
					<div className="flex items-center justify-between py-2">
						<div>
							<p className="text-sm font-medium text-gray-900">自动备份</p>
							<p className="text-xs text-gray-500">自动备份您的对话记录和设置</p>
						</div>
						<Badge>每日备份</Badge>
					</div>
					
					<div className="flex items-center justify-between py-2">
						<div>
							<p className="text-sm font-medium text-gray-900">导出数据</p>
							<p className="text-xs text-gray-500">下载您的所有数据副本</p>
						</div>
						<Button variant="outline" size="sm">导出</Button>
					</div>

					<div className="flex items-center justify-between py-2">
						<div>
							<p className="text-sm font-medium  text-red-600">删除账户</p>
							<p className="text-xs text-gray-500">永久删除您的账户和所有数据</p>
						</div>
						<Button variant="outline" size="sm" className="text-red-600 border-red-200 hover:bg-red-50">
							删除
						</Button>
					</div>
				</div>

				<Separator className="my-6" />
				
				{/* 保存按钮 */}
				<div className="flex justify-end">
					<Button className="flex items-center">
						<Save className="h-4 w-4 mr-2" />
						保存所有设置
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
