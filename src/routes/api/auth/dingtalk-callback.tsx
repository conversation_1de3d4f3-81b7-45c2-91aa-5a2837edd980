import { createServerFileRoute } from "@tanstack/react-start/server";
import { json } from "@tanstack/react-start";

/**
 * 钉钉获取用户AccessToken的API代理
 * 解决前端直接调用钉钉API的CORS问题
 */
export const ServerRoute = createServerFileRoute(
	"/api/auth/dingtalk-callback"
).methods({
	POST: async ({ request }) => {
		try {
			const body = await request.json();
			const { clientId, clientSecret, code, grantType } = body;

			// 打印接收到的参数（用于调试）
			console.log("=== API代理接收到的参数 ===");
			console.log("clientId:", clientId);
			console.log("clientSecret:", clientSecret ? `${clientSecret.substring(0, 4)}****${clientSecret.substring(clientSecret.length - 4)}` : 'undefined');
			console.log("code:", code);
			console.log("grantType:", grantType);
			console.log("clientId 长度:", clientId?.length);
			console.log("clientSecret 长度:", clientSecret?.length);
			console.log("code 长度:", code?.length);

			// 验证必需参数
			if (!clientId || !clientSecret || !code || !grantType) {
				console.error("参数验证失败:", {
					clientId: !!clientId,
					clientSecret: !!clientSecret,
					code: !!code,
					grantType: !!grantType
				});
				return json(
					{
						error: "Missing required parameters",
						message: "缺少必需的参数：clientId, clientSecret, code, grantType",
						details: {
							clientId: !!clientId,
							clientSecret: !!clientSecret,
							code: !!code,
							grantType: !!grantType
						}
					},
					{
						status: 400,
					}
				);
			}

			// 准备发送给钉钉API的数据
			const requestData = {
				clientId,
				clientSecret,
				code,
				grantType,
			};

			console.log("=== 发送给钉钉API的数据 ===");
			console.log("请求URL: https://api.dingtalk.com/v1.0/oauth2/userAccessToken");
			console.log("请求数据:", {
				...requestData,
				clientSecret: clientSecret ? `${clientSecret.substring(0, 4)}****${clientSecret.substring(clientSecret.length - 4)}` : 'undefined'
			});

			// 调用钉钉API
			const response = await fetch(
				"https://api.dingtalk.com/v1.0/oauth2/userAccessToken",
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(requestData),
				}
			);

			const data = await response.json();

			console.log("=== 钉钉API响应信息 ===");
			console.log("响应状态码:", response.status);
			console.log("响应状态文本:", response.statusText);
			console.log("响应头:", Object.fromEntries(response.headers.entries()));
			console.log("响应数据:", data);

			if (!response.ok) {
				console.error("=== 钉钉API调用失败详情 ===");
				console.error("HTTP状态:", response.status, response.statusText);
				console.error("错误响应:", data);
				console.error("错误代码:", data.code);
				console.error("错误消息:", data.message);
				console.error("请求ID:", data.requestid);
				return json(
					{
						error: "DingTalk API Error",
						message: data.message || "钉钉API调用失败",
						details: data,
						httpStatus: response.status,
						httpStatusText: response.statusText
					},
					{
						status: response.status,
					}
				);
			}

			console.log("=== 钉钉API调用成功 ===");
			console.log("成功响应数据:", data);
			// 返回成功响应
			return json(data);
		} catch (error) {
			console.error("API代理错误:", error);
			return json(
				{
					error: "Internal Server Error",
					message: error instanceof Error ? error.message : "服务器内部错误",
				},
				{
					status: 500,
				}
			);
		}
	},

	// 处理预检请求
	OPTIONS: async () => {
		return new Response(null, {
			status: 200,
			headers: {
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			},
		});
	},
});
