import { create } from "zustand";
import { persist } from "zustand/middleware";

/**
 * 消息接口
 * 定义单条消息的结构
 */
export interface Message {
	id: string;                    // Unique identifier for the message
	content: string;              // Content of the message
	role: "user" | "assistant";   // Role of the message sender
	timestamp: Date;             // Time when the message was created
}

/**
 * 对话接口
 * 定义单个对话的结构，包含多个消息
 */
export interface Conversation {
	id: string;                  // Unique identifier for the conversation
	title: string;              // Title of the conversation
	messages: Message[];        // Array of messages in the conversation
	createdAt: Date;           // Time when the conversation was created
	updatedAt: Date;          // Time when the conversation was last updated
}

/**
 * 对话存储接口
 * 定义对话存储的所有操作方法
 */
interface ConversationStore {
	conversations: Conversation[];         // Array of all conversations
	currentConversationId: string | null; // ID of the currently active conversation

	// CRUD operations
	createConversation: (title?: string) => string;                     // Create a new conversation
	deleteConversation: (id: string) => void;                          // Delete a conversation by ID
	updateConversationTitle: (id: string, title: string) => void;     // Update conversation title
	setCurrentConversation: (id: string | null) => void;             // Set the current conversation

	// Message operations
	addMessage: (
		conversationId: string,
		content: string,
		role: "user" | "assistant"
	) => void;                                                         // Add a message to a conversation
	getCurrentConversation: () => Conversation | null;                // Get the current conversation

	// Utility
	clearAllConversations: () => void;                               // Clear all conversations
}

/**
 * 对话状态管理Store
 * 使用zustand实现的状态管理，支持持久化存储
 */
export const useConversationStore = create<ConversationStore>()(
	persist(
		(set, get) => ({
			conversations: [],
			currentConversationId: null,

			/**
			 * 创建新对话
			 * @param title - 对话标题，默认为"新对话"
			 * @returns 新创建的对话ID
			 */
			createConversation: (title = "新对话") => {
				const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
				const newConversation: Conversation = {
					id,
					title,
					messages: [],
					createdAt: new Date(),
					updatedAt: new Date(),
				};

				set((state) => ({
					conversations: [newConversation, ...state.conversations],
					currentConversationId: id,
				}));

				return id;
			},

			/**
			 * 删除指定对话
			 * @param id - 要删除的对话ID
			 */
			deleteConversation: (id) => {
				set((state) => {
					const filteredConversations = state.conversations.filter(
						(conv) => conv.id !== id
					);
					const newCurrentId =
						state.currentConversationId === id
							? filteredConversations.length > 0
								? filteredConversations[0].id
								: null
							: state.currentConversationId;

					return {
						conversations: filteredConversations,
						currentConversationId: newCurrentId,
					};
				});
			},

			/**
			 * 更新对话标题
			 * @param id - 要更新的对话ID
			 * @param title - 新的对话标题
			 */
			updateConversationTitle: (id, title) => {
				set((state) => ({
					conversations: state.conversations.map((conv) =>
						conv.id === id ? { ...conv, title, updatedAt: new Date() } : conv
					),
				}));
			},

			/**
			 * 设置当前对话
			 * @param id - 要设置为当前的对话ID，传null表示不设置任何对话为当前对话
			 */
			setCurrentConversation: (id) => {
				set({ currentConversationId: id });
			},

			/**
			 * 向指定对话添加消息
			 * @param conversationId - 对话ID
			 * @param content - 消息内容
			 * @param role - 消息发送者角色
			 */
			addMessage: (conversationId, content, role) => {
				const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
				const newMessage: Message = {
					id: messageId,
					content,
					role,
					timestamp: new Date(),
				};

				set((state) => ({
					conversations: state.conversations.map((conv) =>
						conv.id === conversationId
							? {
									...conv,
									messages: [...conv.messages, newMessage],
									updatedAt: new Date(),
								}
							: conv
					),
				}));
			},

			/**
			 * 获取当前对话
			 * @returns 当前对话对象，如果没有则返回null
			 */
			getCurrentConversation: () => {
				const state = get();
				return (
					state.conversations.find(
						(conv) => conv.id === state.currentConversationId
					) || null
				);
			},

			/**
			 * 清空所有对话
			 */
			clearAllConversations: () => {
				set({ conversations: [], currentConversationId: null });
			},
		}),
		{
			name: "conversation-store",
			partialize: (state) => ({
				conversations: state.conversations,
				currentConversationId: state.currentConversationId,
			}),
		}
	)
);