步骤六：获取用户授权信息
访问已经构造的钉钉应用授权登录访问地址/通过扫描二维码的方式实现用户登录授权。

当用户同意授权后，此时会携带 authCode 到步骤五重定向 URL 的路径后，例如：http://example.com?code=f85c6*****7b77&authCode=f85c6******e49e87b77。

此处，code 和 authCode 一致，取任一即可。
根据 authCode，调用服务端获取用户token接口，获取用户个人token。

根据用户个人token，调用获取用户通讯录个人信息接口，获取授权用户个人信息。


获取用户token新版SDK
更新于 2025-07-01
调用本接口获取用户token。

说明
在使用accessToken时，请注意：
accessToken的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的accessToken。
开发者需要缓存accessToken，用于后续接口的调用。因为每个应用的accessToken是彼此独立的，所以进行缓存时需要区分应用来进行存储。
权限
要调用此API，需要以下权限之一。

应用类型

是否支持

权限

API Explorer调试

企业内部应用

支持

默认开通

API Explorer

第三方企业应用

支持

默认开通

API Explorer

第三方个人应用

支持

默认开通

API Explorer

请求方法
1234567891011
POST /v1.0/oauth2/userAccessToken HTTP/1.1
Host:api.dingtalk.com
Content-Type:application/json

{
  "clientId" : "String",
  "clientSecret" : "String",
  "code" : "String",
  "refreshToken" : "String",
  "grantType" : "String"

Body参数
名称

类型

是否必填

描述

clientId

String

是

应用id。可使用扫码登录应用或者第三方个人小程序的appId。

企业内部应用传应用的AppKey

第三方企业应用传应用的SuiteKey

第三方个人应用传应用的AppId

clientSecret

String

是

应用密钥。

企业内部应用传应用的AppSecret

第三方企业应用传应用的SuiteSecret

第三方个人应用传应用的AppSecret

code

String

否

OAuth 2.0 临时授权码，第三方企业应用需要接入统一授权套件/获取登录用户的访问凭证，获取临时授权码authCode。

refreshToken

String

否

OAuth2.0刷新令牌，从返回结果里面获取。

说明
过期时间是30天。
grantType

String

否

如果使用授权码换token，传authorization_code。

如果使用刷新token换用户token，传refresh_token。

说明
使用授权码方式：code必须填写。
使用刷新token：refreshToken必填填写。
返回参数
名称

类型

描述

accessToken

String

生成的accessToken。

refreshToken

String

生成的refresh_token。可以使用此刷新token，定期的获取用户的accessToken

expireIn

Long

超时时间，单位秒。

corpId

String

所选企业corpId。

示例
请求示例

HTTP
1234567891011
POST /v1.0/oauth2/userAccessToken HTTP/1.1
Host:api.dingtalk.com
Content-Type:application/json

{
  "clientId" : "dingxxx",
  "clientSecret" : "1234",
  "code" : "abcd",
  "refreshToken" : "abcd",
  "grantType" : "authorization_code"

JavaPythonPHPGoNode.jsC#C++
返回示例

123456789


