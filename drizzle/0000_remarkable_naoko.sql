CREATE TABLE `statistics` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`totalRequestTimes` integer DEFAULT 0 NOT NULL,
	`totalTokenUsage` integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`dingTalkUnionId` text,
	`email` text,
	`password` text,
	`isAdmin` integer DEFAULT false NOT NULL,
	`token` integer DEFAULT 20000 NOT NULL,
	`requestTimes` integer DEFAULT 0 NOT NULL,
	`dingTalkUserId` text,
	`name` text,
	`avatar` text,
	`mobile` text,
	`createdAt` text DEFAULT (datetime('now')) NOT NULL,
	`updatedAt` text DEFAULT (datetime('now')) NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_dingTalkUnionId_unique` ON `users` (`dingTalkUnionId`);--> statement-breakpoint
CREATE UNIQUE INDEX `users_email_unique` ON `users` (`email`);